#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试布捆坐标、直径和托盘四个顶点坐标上发功能
"""

import json
import time

def test_cloth_coordinates_upload():
    """测试布捆坐标和直径上发（按照正确的协议格式）"""
    print("=== 测试布捆坐标和直径上发（协议格式）===")

    # 模拟布捆检测结果
    mock_cloth_results = [
        [100, 200, 2500, 15.5, 1, 180, 800],  # [x, y, z, angle, order, diameter, length]
        [300, 400, 2600, -10.2, 2, 175, 820],
        [500, 150, 2550, 5.8, 0, 185, 790],   # order=0表示下层布捆
        [700, 350, 2580, 20.1, 0, 170, 810]   # order=0表示下层布捆
    ]

    # 分离可抓取和下层布捆
    graspable_points = []
    next_layer_points = []

    for result in mock_cloth_results:
        x, y, z, angle, order, diameter, length = result
        if order > 0:
            graspable_points.append([x, y, z, angle, order, diameter, length])
        else:
            next_layer_points.append([x, y, z, angle, 0, diameter, length])

    # 按order排序可抓取的布捆
    graspable_points.sort(key=lambda x: x[4])

    # 构建所有布捆的坐标信息（按协议格式：[x, y, z, angle, diameter, length]）
    all_points = []
    grasping_order = []

    # 添加可抓取的布捆
    for entry in graspable_points:
        x, y, z, angle, order, diameter, length = entry
        all_points.append([x, y, z, angle, diameter, length])
        grasping_order.append(order)

    # 添加下层布捆
    for entry in next_layer_points:
        x, y, z, angle, _, diameter, length = entry
        all_points.append([x, y, z, angle, diameter, length])

    # 构建符合协议的上发消息
    box_feedback = {
        "eid": "VISUAL",
        "dev": "DC1",
        "type": "boxFeedback",
        "taskNum": "box20240305000001",
        "data": {
            "result": "pass",
            "code": 0,
            "point": all_points,  # 按协议格式使用point字段
            # 可以增加的额外字段
            "graspingOrder": grasping_order,
            "totalCount": len(all_points),
            "graspableCount": len(graspable_points),
            "nextLayerCount": len(next_layer_points),
            "wrong_diameter": "none"
        },
        "sendTime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    }

    print("布捆坐标上发消息（协议格式）:")
    print(json.dumps(box_feedback, ensure_ascii=False, indent=2))

    print(f"\n所有布捆信息 (共{len(all_points)}个):")
    for i, point in enumerate(all_points):
        x, y, z, angle, diameter, length = point
        if i < len(graspable_points):
            order = grasping_order[i]
            print(f"  可抓取布捆{order}: 坐标({x}, {y}, {z}), 角度{angle}°, 直径{diameter}mm, 长度{length}mm")
        else:
            print(f"  下层布捆{i-len(graspable_points)+1}: 坐标({x}, {y}, {z}), 角度{angle}°, 直径{diameter}mm, 长度{length}mm")

def test_pallet_corners_upload():
    """测试托盘四个顶点坐标上发"""
    print("\n=== 测试托盘四个顶点坐标上发 ===")
    
    # 模拟托盘几何信息
    import math
    
    # 托盘中心和尺寸
    center_x, center_y, center_z = -300, -25, 2230
    original_length, original_width = 1800, 1240
    rotation_angle = -1.5  # 逆时针旋转1.5度
    
    # 计算旋转后的四个角点
    angle_rad = math.radians(rotation_angle)
    half_l = original_length / 2
    half_w = original_width / 2
    
    # 原始四个角点（相对于中心点）
    corners = [
        [-half_l, -half_w],  # 左上
        [half_l, -half_w],   # 右上
        [half_l, half_w],    # 右下
        [-half_l, half_w]    # 左下
    ]
    
    # 应用旋转变换
    rotated_corners = []
    for corner in corners:
        rotated_x = corner[0] * math.cos(angle_rad) - corner[1] * math.sin(angle_rad)
        rotated_y = corner[0] * math.sin(angle_rad) + corner[1] * math.cos(angle_rad)
        rotated_corners.append([center_x + rotated_x, center_y + rotated_y])
    
    # 构建palletCenter反馈消息
    lt = [rotated_corners[0][0], rotated_corners[0][1], center_z]  # 左上
    rt = [rotated_corners[1][0], rotated_corners[1][1], center_z]  # 右上  
    lb = [rotated_corners[3][0], rotated_corners[3][1], center_z]  # 左下
    rb = [rotated_corners[2][0], rotated_corners[2][1], center_z]  # 右下
    center_pt = [center_x, center_y, center_z]   # 中心点
    
    pallet_center_feedback = {
        "type": "palletCenterFeedback",
        "taskNum": "TEST_002",
        "data": {
            "result": "pass",
            "code": [0],
            "center": [lt, rt, lb, rb, center_pt],
            "angle": rotation_angle
        },
        "sendTime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    }
    
    print("托盘中心点上发消息:")
    print(json.dumps(pallet_center_feedback, ensure_ascii=False, indent=2))
    
    print(f"\n托盘四个顶点坐标:")
    print(f"  左上角: ({lt[0]:.2f}, {lt[1]:.2f}, {lt[2]})")
    print(f"  右上角: ({rt[0]:.2f}, {rt[1]:.2f}, {rt[2]})")
    print(f"  左下角: ({lb[0]:.2f}, {lb[1]:.2f}, {lb[2]})")
    print(f"  右下角: ({rb[0]:.2f}, {rb[1]:.2f}, {rb[2]})")
    print(f"  中心点: ({center_pt[0]}, {center_pt[1]}, {center_pt[2]})")
    print(f"  旋转角度: {rotation_angle}度")

def test_pallet_size_upload():
    """测试托盘尺寸信息上发"""
    print("\n=== 测试托盘尺寸信息上发 ===")
    
    # 模拟托盘尺寸信息
    import math
    
    center_x, center_y, center_z = -300, -25, 2230
    original_length, original_width = 1800, 1240
    rotation_angle = -1.5
    
    # 计算旋转后的四个角点（同上）
    angle_rad = math.radians(rotation_angle)
    half_l = original_length / 2
    half_w = original_width / 2
    
    corners = [
        [-half_l, -half_w],  # 左上
        [half_l, -half_w],   # 右上
        [half_l, half_w],    # 右下
        [-half_l, half_w]    # 左下
    ]
    
    rotated_corners = []
    for corner in corners:
        rotated_x = corner[0] * math.cos(angle_rad) - corner[1] * math.sin(angle_rad)
        rotated_y = corner[0] * math.sin(angle_rad) + corner[1] * math.cos(angle_rad)
        rotated_corners.append([center_x + rotated_x, center_y + rotated_y])
    
    # 计算旋转后的边界框尺寸
    x_coords = [corner[0] for corner in rotated_corners]
    y_coords = [corner[1] for corner in rotated_corners]
    
    min_x, max_x = min(x_coords), max(x_coords)
    min_y, max_y = min(y_coords), max(y_coords)
    
    rotated_length = max_x - min_x
    rotated_width = max_y - min_y
    distance_to_top = max_y - center_y
    distance_to_bottom = center_y - min_y
    
    # 构建四个角点坐标
    corner_points = [
        [rotated_corners[0][0], rotated_corners[0][1], center_z],  # 左上
        [rotated_corners[1][0], rotated_corners[1][1], center_z],  # 右上  
        [rotated_corners[3][0], rotated_corners[3][1], center_z],  # 左下
        [rotated_corners[2][0], rotated_corners[2][1], center_z]   # 右下
    ]
    
    pallet_size_feedback = {
        "type": "palletSizeFeedback",
        "taskNum": "TEST_003_size",
        "data": {
            "result": "pass",
            "code": 0,
            "size": {
                "center": [center_x, center_y, center_z],
                "length": round(rotated_length, 2),
                "width": round(rotated_width, 2),
                "distance_to_top": round(distance_to_top, 2),
                "distance_to_bottom": round(distance_to_bottom, 2),
                "corners": corner_points,
                "rotation_angle": rotation_angle
            }
        },
        "sendTime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    }
    
    print("托盘尺寸上发消息:")
    print(json.dumps(pallet_size_feedback, ensure_ascii=False, indent=2))
    
    print(f"\n托盘尺寸信息:")
    print(f"  中心点: ({center_x}, {center_y}, {center_z})")
    print(f"  旋转后长度: {rotated_length:.2f}mm")
    print(f"  旋转后宽度: {rotated_width:.2f}mm")
    print(f"  距离上边: {distance_to_top:.2f}mm")
    print(f"  距离下边: {distance_to_bottom:.2f}mm")
    print(f"  旋转角度: {rotation_angle}度")

if __name__ == "__main__":
    test_cloth_coordinates_upload()
    test_pallet_corners_upload()
    test_pallet_size_upload()
    
    print("\n=== 测试完成 ===")
    print("✅ 布捆坐标和直径已正确包含在points和nextPoints数组中")
    print("✅ 托盘四个顶点坐标已正确上发到palletCenter消息中")
    print("✅ 托盘尺寸信息已包含四个顶点坐标和旋转角度")
